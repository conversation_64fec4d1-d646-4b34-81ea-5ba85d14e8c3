# Relacje między komponentami Chat

## Struktura komponentów:

### 1. Chat (główny komponent)
- Lokalizacja: `src/components/chat/chat.tsx`
- Odpowiedzialność: Zarządzanie listą pokojów, użytkowników, tworzenie nowych czatów
- Używa: ChatRoom, OnlineStatus, ProjectIcon
- Stan: chatRooms, selectedRoom, users, projects, onlineUsers (z socket)

### 2. ChatRoom (pokój czatu)
- Lokalizacja: `src/components/chat/chat-room.tsx`
- Odpowiedzialność: Wyświetlani<PERSON> wiadomości, obsługa wysyłania, typing indicators
- Używa: Message, ChatInput
- Otrzymuje: room, users (z Chat)
- Stan: messages, typingUsers, loading

### 3. ChatInput (pole wprowadzania wiadomości)
- Lokalizacja: `src/components/chat/chat-input.tsx`
- Odpowiedzialność: Wprowadzanie tekstu, emoji, tagowanie użytkowników
- Używa: MentionInput
- Otrzymuje: users, onSendMessage, onTyping (z ChatRoom)
- Stan: message, showMentions, mentionSearch, mentionPosition

### 4. MentionInput (dropdown do tagowania)
- Lokalizacja: `src/components/chat/mention-input.tsx`
- Odpowiedzialność: Autocomplete dla tagowania użytkowników
- Otrzymuje: users, searchTerm, position, onSelect, onClose (z ChatInput)
- Stan: selectedIndex, filteredUsers

### 5. Message (pojedyncza wiadomość)
- Lokalizacja: `src/components/chat/message.tsx`
- Odpowiedzialność: Wyświetlanie wiadomości z avatarem i czasem
- Używa: MessageContent
- Otrzymuje: message, showAvatar, isOwn, users, currentUserId (z ChatRoom)

### 6. MessageContent (treść wiadomości z tagami)
- Lokalizacja: `src/components/chat/message-content.tsx`
- Odpowiedzialność: Parsowanie i wyświetlanie tagów @[userId] w wiadomościach
- Otrzymuje: content, users, currentUserId (z Message)
- Funkcje: parseMentions() - konwertuje @[userId] na kolorowe tagi

### 7. OnlineStatus (wskaźnik online)
- Lokalizacja: `src/components/chat/online-status.tsx`
- Odpowiedzialność: Wyświetlanie statusu online/offline
- Otrzymuje: isOnline, size, className

## Przepływ danych:

### Tagowanie użytkowników:
1. Użytkownik wpisuje "@" w ChatInput
2. ChatInput wykrywa trigger i pokazuje MentionInput
3. MentionInput filtruje użytkowników na podstawie wpisanego tekstu
4. Po wyborze użytkownika, ChatInput wstawia @[userId] do wiadomości
5. Wiadomość jest wysyłana z tagiem @[userId]
6. MessageContent parsuje @[userId] i wyświetla jako kolorowy tag

### Status online:
1. SocketProvider śledzi onlineUsers (Set<string>)
2. Chat otrzymuje onlineUsers z useSocket()
3. Chat przekazuje isUserOnline() do komponentów
4. OnlineStatus wyświetla zielony/szary wskaźnik

### Wysyłanie wiadomości:
1. ChatInput -> ChatRoom.handleSendMessage()
2. ChatRoom wysyła POST do API
3. ChatRoom emituje przez socket
4. Socket server broadcastuje do innych użytkowników
5. Wszyscy otrzymują nową wiadomość przez socket

## Kluczowe funkcje:

### W ChatInput:
- checkForMention() - wykrywa "@" i pokazuje dropdown
- handleMentionSelect() - wstawia @[userId] do tekstu
- handleAtSignClick() - programowo wstawia "@"

### W MessageContent:
- parseMentions() - regex @\[([^\]]+)\] do znajdowania tagów
- Konwertuje userId na displayName z listy users

### W SocketProvider:
- Obsługa user-online/user-offline eventów
- Śledzenie onlineUsers jako Set<string>
- Automatyczne offline przy zamknięciu karty/okna

## Format tagów:
- W bazie danych: "@[cmdv5at8u0008bukm38vcfxg5]"
- W UI: "@Krystian Admin" (kolorowy tag)
- Podświetlenie: niebieskie dla innych, primary dla siebie
